# AI Studio 开发架构设计文档

## 📋 文档信息

| 项目信息 | 详细说明 |
|---------|---------|
| **项目名称** | AI Studio - 本地AI助手桌面应用 |
| **文档版本** | v3.0 深度优化完整架构设计版 |
| **目标平台** | Windows 和 macOS 桌面应用 |
| **分辨率支持** | 最小800×600，默认1200×800 |
| **核心技术栈** | Vue3.5+ + Vite7.0+ + Tauri2.x + Rust + SQLite + ChromaDB |
| **样式技术** | Tailwind CSS + SCSS |
| **主题系统** | 深色/浅色主题切换 |
| **国际化支持** | 中文/英文双语切换 |
| **文档状态** | 深度优化的完整架构设计版 |
| **创建日期** | 2025年1月 |

---

## 📋 详细目录

### 第一部分：项目概述与规划
- [1.1 项目背景与需求分析](#11-项目背景与需求分析)
- [1.2 技术栈选型与决策](#12-技术栈选型与决策)
- [1.3 整体架构设计](#13-整体架构设计)
- [1.4 核心功能特性](#14-核心功能特性)

### 第二部分：前端架构设计
- [2.1 前端目录结构详解](#21-前端目录结构详解)
- [2.2 Vue3组件设计规范](#22-vue3组件设计规范)
- [2.3 Tailwind CSS + SCSS样式方案](#23-tailwind-css--scss样式方案)
- [2.4 状态管理与路由设计](#24-状态管理与路由设计)

### 第三部分：后端架构设计
- [3.1 Rust后端目录结构](#31-rust后端目录结构)
- [3.2 Tauri集成与命令系统](#32-tauri集成与命令系统)
- [3.3 AI推理引擎模块](#33-ai推理引擎模块)
- [3.4 后端服务架构设计](#34-后端服务架构设计)

### 第四部分：核心功能模块
- [4.1 聊天功能模块](#41-聊天功能模块)
- [4.2 知识库模块](#42-知识库模块)
- [4.3 模型管理模块](#43-模型管理模块)
- [4.4 多模态交互模块](#44-多模态交互模块)
- [4.5 网络功能模块](#45-网络功能模块)
- [4.6 插件系统模块](#46-插件系统模块)

### 第五部分：数据层设计
- [5.1 SQLite关系型数据库](#51-sqlite关系型数据库)
- [5.2 ChromaDB向量数据库](#52-chromadb向量数据库)
- [5.3 数据库关系图与数据流](#53-数据库关系图与数据流)

### 第六部分：用户界面设计
- [6.1 组件库设计规范](#61-组件库设计规范)
- [6.2 主题系统与样式指南](#62-主题系统与样式指南)
- [6.3 国际化设计方案](#63-国际化设计方案)

### 第七部分：系统流程设计
- [7.1 用户操作流程](#71-用户操作流程)
- [7.2 数据处理逻辑](#72-数据处理逻辑)
- [7.3 AI推理流程](#73-ai推理流程)

### 第八部分：API接口设计
- [8.1 Tauri Invoke通信协议](#81-tauri-invoke通信协议)
- [8.2 前后端接口规范](#82-前后端接口规范)
- [8.3 接口安全与验证](#83-接口安全与验证)

### 第九部分：错误处理与性能优化
- [9.1 异常捕获策略](#91-异常捕获策略)
- [9.2 性能优化策略](#92-性能优化策略)
- [9.3 日志记录机制](#93-日志记录机制)

### 第十部分：开发与部署
- [10.1 开发环境配置](#101-开发环境配置)
- [10.2 构建与打包](#102-构建与打包)
- [10.3 测试策略](#103-测试策略)
- [10.4 部署与发布](#104-部署与发布)

---

## 第一部分：项目概述与规划

### 1.1 项目背景与需求分析

#### 1.1.1 项目背景

AI Studio 是一个专为桌面环境设计的本地AI助手应用，旨在为用户提供强大的AI交互体验。项目基于现代化的技术栈，结合了前端的灵活性和后端的高性能，为用户提供流畅、安全、可扩展的AI服务。

**核心价值主张：**
- **本地化部署**：数据隐私安全，无需依赖云端服务
- **多模态交互**：支持文本、语音、图像等多种交互方式
- **知识库管理**：智能文档管理和检索系统
- **插件生态**：可扩展的插件系统，支持自定义功能
- **跨平台支持**：原生支持Windows和macOS桌面环境

#### 1.1.2 需求分析

**功能性需求：**

| 需求类别 | 具体需求 | 优先级 |
|---------|---------|--------|
| **AI聊天** | 多轮对话、上下文理解、流式响应 | 高 |
| **知识库** | 文档导入、智能检索、RAG增强 | 高 |
| **模型管理** | 本地模型下载、切换、性能监控 | 高 |
| **多模态** | OCR识别、语音转换、图像分析 | 中 |
| **网络功能** | 局域网共享、设备发现、文件传输 | 中 |
| **插件系统** | 插件安装、管理、开发支持 | 低 |

**非功能性需求：**

| 需求类别 | 具体要求 | 指标 |
|---------|---------|------|
| **性能** | 响应时间、内存占用、启动速度 | <100ms UI响应，<500MB内存 |
| **可用性** | 界面友好、操作简单、错误提示 | 用户满意度>90% |
| **可靠性** | 系统稳定、数据安全、错误恢复 | 99.9%可用性 |
| **可扩展性** | 模块化设计、插件支持、API开放 | 支持100+插件 |
| **兼容性** | 操作系统、硬件配置、文件格式 | Windows 10+, macOS 10.15+ |

### 1.2 技术栈选型与决策

#### 1.2.1 前端技术栈

**Vue 3.5+ 选型理由：**
- **组合式API**：更好的TypeScript支持和代码组织
- **性能优化**：Proxy响应式系统，更高效的更新机制
- **生态成熟**：丰富的组件库和工具链支持
- **学习成本**：相对较低的学习曲线

**Vite 7.0+ 选型理由：**
- **快速启动**：基于ESM的开发服务器，秒级启动
- **热更新**：高效的HMR机制，开发体验优秀
- **构建优化**：基于Rollup的生产构建，包体积小
- **插件生态**：丰富的插件系统，扩展性强

**TypeScript 选型理由：**
- **类型安全**：编译时错误检查，减少运行时错误
- **开发体验**：智能提示、重构支持、代码导航
- **团队协作**：统一的代码规范和接口定义
- **生态支持**：主流框架和库的完整类型支持

#### 1.2.2 后端技术栈

**Tauri 2.x 选型理由：**
- **安全性**：Rust内存安全特性，防止常见安全漏洞
- **性能**：原生性能，资源占用低
- **跨平台**：统一的API，支持多平台部署
- **包体积**：相比Electron更小的安装包

**Rust 选型理由：**
- **内存安全**：所有权系统防止内存泄漏和数据竞争
- **高性能**：零成本抽象，接近C++的性能
- **并发支持**：优秀的异步编程模型
- **生态发展**：快速发展的生态系统

**SQLite 选型理由：**
- **零配置**：嵌入式数据库，无需安装配置
- **ACID支持**：完整的事务支持，数据一致性保证
- **跨平台**：广泛的平台支持
- **性能优秀**：对于桌面应用的数据量，性能表现优异

**ChromaDB 选型理由：**
- **向量存储**：专为AI应用设计的向量数据库
- **易于集成**：简单的API接口，快速集成
- **性能优化**：针对相似度搜索优化
- **开源免费**：活跃的开源社区支持

### 1.3 整体架构设计

#### 1.3.1 系统架构图

```
AI Studio 整体架构图:

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                AI Studio 系统架构                                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                            前端层 (Frontend Layer)                          │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │   │
│  │  │   Vue 3.5+      │  │  Tailwind CSS   │  │   TypeScript    │             │   │
│  │  │   组件系统       │  │   样式系统      │  │   类型系统      │             │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘             │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │   │
│  │  │     Pinia       │  │  Vue Router     │  │     Vite        │             │   │
│  │  │   状态管理      │  │   路由管理      │  │   构建工具      │             │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘             │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                            │
│                                        │ Tauri IPC                                  │
│                                        ↓                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                           后端层 (Backend Layer)                            │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │   │
│  │  │    Tauri 2.x    │  │      Rust       │  │     Tokio       │             │   │
│  │  │   桌面框架      │  │   系统语言      │  │   异步运行时     │             │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘             │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │   │
│  │  │   AI引擎模块     │  │   网络模块      │  │   插件系统      │             │   │
│  │  │   推理服务      │  │   P2P通信       │  │   扩展支持      │             │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘             │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                            │
│                                        │ 数据访问                                   │
│                                        ↓                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                           数据层 (Data Layer)                               │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐                        ┌─────────────────┐             │   │
│  │  │     SQLite      │                        │    ChromaDB     │             │   │
│  │  │   关系型数据库   │ ←─────── 数据同步 ────→ │   向量数据库     │             │   │
│  │  │                 │                        │                 │             │   │
│  │  │ • 用户配置      │                        │ • 文档向量      │             │   │
│  │  │ • 聊天记录      │                        │ • 语义搜索      │             │   │
│  │  │ • 模型信息      │                        │ • 相似度计算    │             │   │
│  │  │ • 系统日志      │                        │ • 知识检索      │             │   │
│  │  └─────────────────┘                        └─────────────────┘             │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                          AI推理层 (AI Inference Layer)                      │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │   │
│  │  │     Candle      │  │   LLaMA.cpp     │  │  ONNX Runtime   │             │   │
│  │  │   Rust AI框架   │  │   LLM推理引擎   │  │   通用推理引擎   │             │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘             │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │   │
│  │  │   本地模型库     │  │   RAG检索增强   │  │   多模态处理     │             │   │
│  │  │   模型管理      │  │   知识库集成    │  │   图像/语音     │             │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘             │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                     │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

#### 1.3.2 技术架构特点

**分层架构设计：**
- **前端层**：负责用户界面和交互逻辑
- **后端层**：处理业务逻辑和系统服务
- **数据层**：管理数据存储和检索
- **AI推理层**：提供AI能力和模型服务

**核心设计原则：**
- **模块化**：各层职责清晰，低耦合高内聚
- **可扩展**：支持插件系统和功能扩展
- **高性能**：异步处理和资源优化
- **安全性**：数据加密和权限控制

### 1.4 核心功能特性

#### 1.4.1 功能模块概览

**六大核心模块：**

| 模块名称 | 主要功能 | 技术实现 |
|---------|---------|---------|
| **聊天功能** | 多轮对话、上下文管理、流式响应 | Vue3 + Rust + AI引擎 |
| **知识库** | 文档管理、智能检索、RAG增强 | ChromaDB + SQLite |
| **模型管理** | 模型下载、切换、性能监控 | Rust + 本地存储 |
| **多模态** | OCR、语音转换、图像分析 | ONNX + 专用模型 |
| **网络功能** | 局域网共享、设备发现 | P2P + mDNS |
| **插件系统** | 插件开发、安装、管理 | WebAssembly + API |

#### 1.4.2 特色功能亮点

**智能对话系统：**
- 支持多种AI模型切换
- 上下文感知和记忆功能
- 流式响应提升用户体验
- 自定义角色和提示词

**知识库管理：**
- 支持多种文档格式导入
- 智能分块和向量化处理
- 语义搜索和相关性排序
- RAG增强生成准确性

**本地化优势：**
- 完全离线运行，保护隐私
- 无需网络依赖，稳定可靠
- 自定义模型和配置
- 数据完全掌控

---

## 第二部分：前端架构设计

### 2.1 前端目录结构详解

#### 2.1.1 项目目录结构

```
src/
├── main.ts                 # 应用入口文件
├── App.vue                 # 根组件
├── assets/                 # 静态资源
│   ├── images/            # 图片资源
│   ├── icons/             # 图标资源
│   └── fonts/             # 字体资源
├── components/            # 组件库
│   ├── common/            # 通用组件
│   │   ├── Button.vue     # 按钮组件
│   │   ├── Modal.vue      # 模态框组件
│   │   ├── Input.vue      # 输入框组件
│   │   └── Loading.vue    # 加载组件
│   ├── layout/            # 布局组件
│   │   ├── Header.vue     # 头部组件
│   │   ├── Sidebar.vue    # 侧边栏组件
│   │   └── Footer.vue     # 底部组件
│   └── business/          # 业务组件
│       ├── ChatMessage.vue    # 聊天消息组件
│       ├── KnowledgeCard.vue  # 知识库卡片
│       └── ModelSelector.vue  # 模型选择器
├── views/                 # 页面视图
│   ├── Chat/              # 聊天页面
│   ├── Knowledge/         # 知识库页面
│   ├── Models/            # 模型管理页面
│   ├── Settings/          # 设置页面
│   └── Plugins/           # 插件页面
├── stores/                # 状态管理
│   ├── chat.ts            # 聊天状态
│   ├── knowledge.ts       # 知识库状态
│   ├── models.ts          # 模型状态
│   ├── settings.ts        # 设置状态
│   └── theme.ts           # 主题状态
├── composables/           # 组合式函数
│   ├── useChat.ts         # 聊天逻辑
│   ├── useKnowledge.ts    # 知识库逻辑
│   └── useTheme.ts        # 主题逻辑
├── utils/                 # 工具函数
│   ├── api.ts             # API封装
│   ├── format.ts          # 格式化工具
│   └── validation.ts      # 验证工具
├── types/                 # 类型定义
│   ├── chat.ts            # 聊天类型
│   ├── knowledge.ts       # 知识库类型
│   └── common.ts          # 通用类型
├── styles/                # 样式文件
│   ├── main.scss          # 主样式文件
│   ├── variables.scss     # 变量定义
│   └── components.scss    # 组件样式
└── router/                # 路由配置
    └── index.ts           # 路由定义
```

#### 2.1.2 组件分类与职责

**通用组件 (common/)**
- **基础交互组件**：Button、Input、Select、Checkbox等
- **反馈组件**：Loading、Toast、Modal、Confirm等
- **导航组件**：Tabs、Pagination、Breadcrumb等
- **数据展示组件**：Table、Card、List、Tree等

**布局组件 (layout/)**
- **容器组件**：Header、Sidebar、Footer、Main等
- **导航组件**：Navigation、Menu、Breadcrumb等
- **响应式组件**：Grid、Flex、Container等

**业务组件 (business/)**
- **聊天相关**：ChatMessage、ChatInput、MessageList等
- **知识库相关**：DocumentCard、SearchBox、CategoryTree等
- **模型相关**：ModelCard、ModelSelector、PerformanceChart等

### 2.2 Vue3组件设计规范

#### 2.2.1 组件设计原则

**设计原则：**
- **单一职责**：每个组件只负责一个功能
- **可复用性**：组件可在不同场景下复用
- **可组合性**：组件可以组合成更复杂的功能
- **可测试性**：组件易于单元测试
- **可维护性**：代码清晰，易于理解和修改

#### 2.2.2 组件开发规范

**文件命名规范：**
```
PascalCase.vue     # 组件文件名使用大驼峰命名
├── Button.vue     # ✅ 正确
├── button.vue     # ❌ 错误
├── my-button.vue  # ❌ 错误
└── MyButton.vue   # ✅ 正确
```

**组件结构规范：**
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, computed, onMounted } from 'vue'

// 定义接口
interface Props {
  // 属性定义
}

interface Emits {
  // 事件定义
}

// 组件属性
const props = withDefaults(defineProps<Props>(), {
  // 默认值
})

// 组件事件
const emit = defineEmits<Emits>()

// 响应式数据
const state = ref()

// 计算属性
const computed = computed(() => {
  // 计算逻辑
})

// 生命周期
onMounted(() => {
  // 初始化逻辑
})

// 方法定义
const handleClick = () => {
  // 事件处理
}
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

#### 2.2.3 核心组件实现

**Button组件示例：**
```vue
<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <Icon v-if="loading" name="loading" class="animate-spin mr-2" />
    <Icon v-else-if="icon" :name="icon" class="mr-2" />
    <span v-if="$slots.default"><slot /></span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from './Icon.vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  icon?: string
  block?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  block: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => [
  'btn',
  `btn-${props.variant}`,
  `btn-${props.size}`,
  {
    'btn-block': props.block,
    'btn-loading': props.loading,
    'btn-disabled': props.disabled
  }
])

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
.btn {
  @apply inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;

  &-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  &-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  &-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  &-ghost {
    @apply bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }

  &-xs {
    @apply px-2 py-1 text-xs;
  }

  &-sm {
    @apply px-3 py-1.5 text-sm;
  }

  &-md {
    @apply px-4 py-2 text-sm;
  }

  &-lg {
    @apply px-6 py-3 text-base;
  }

  &-xl {
    @apply px-8 py-4 text-lg;
  }

  &-block {
    @apply w-full;
  }

  &-loading,
  &-disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}
</style>
```

### 2.3 Tailwind CSS + SCSS样式方案

#### 2.3.1 样式架构设计

**样式层次结构：**
```
styles/
├── main.scss              # 主样式文件
├── variables.scss          # SCSS变量定义
├── mixins.scss            # SCSS混入函数
├── base.scss              # 基础样式重置
├── components.scss        # 组件样式
├── utilities.scss         # 工具类样式
└── themes/                # 主题样式
    ├── light.scss         # 浅色主题
    ├── dark.scss          # 深色主题
    └── high-contrast.scss # 高对比度主题
```

**Tailwind配置：**
```javascript
// tailwind.config.js
module.exports = {
  content: ['./src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a'
        },
        gray: {
          50: '#f9fafb',
          500: '#6b7280',
          900: '#111827'
        }
      },
      fontFamily: {
        sans: ['Inter', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace']
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem'
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out'
      }
    }
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography')
  ]
}
```

#### 2.3.2 主题系统实现

**CSS变量定义：**
```scss
// variables.scss
:root {
  // 颜色系统
  --color-primary: #3b82f6;
  --color-secondary: #6b7280;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  // 背景颜色
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;

  // 文字颜色
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;

  // 边框颜色
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;

  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  // 圆角
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;

  // 间距
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
}

// 深色主题
.theme-dark {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;

  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;

  --border-primary: #374151;
  --border-secondary: #4b5563;
}
```

### 2.4 状态管理与路由设计

#### 2.4.1 Pinia状态管理

**状态管理架构：**
```
stores/
├── index.ts               # Store入口文件
├── chat.ts               # 聊天状态管理
├── knowledge.ts          # 知识库状态管理
├── models.ts             # 模型状态管理
├── settings.ts           # 设置状态管理
├── theme.ts              # 主题状态管理
└── user.ts               # 用户状态管理
```

**Chat Store示例：**
```typescript
// stores/chat.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  tokens?: number
}

export interface ChatSession {
  id: string
  title: string
  messages: Message[]
  model: string
  createdAt: number
  updatedAt: number
}

export const useChatStore = defineStore('chat', () => {
  // 状态
  const sessions = ref<ChatSession[]>([])
  const currentSessionId = ref<string | null>(null)
  const isLoading = ref(false)
  const isStreaming = ref(false)

  // 计算属性
  const currentSession = computed(() =>
    sessions.value.find(s => s.id === currentSessionId.value)
  )

  const currentMessages = computed(() =>
    currentSession.value?.messages || []
  )

  // 方法
  const createSession = (title: string, model: string): string => {
    const session: ChatSession = {
      id: generateId(),
      title,
      messages: [],
      model,
      createdAt: Date.now(),
      updatedAt: Date.now()
    }

    sessions.value.unshift(session)
    currentSessionId.value = session.id

    return session.id
  }

  const addMessage = (sessionId: string, message: Omit<Message, 'id' | 'timestamp'>) => {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      const newMessage: Message = {
        ...message,
        id: generateId(),
        timestamp: Date.now()
      }

      session.messages.push(newMessage)
      session.updatedAt = Date.now()
    }
  }

  const deleteSession = (sessionId: string) => {
    const index = sessions.value.findIndex(s => s.id === sessionId)
    if (index > -1) {
      sessions.value.splice(index, 1)

      if (currentSessionId.value === sessionId) {
        currentSessionId.value = sessions.value[0]?.id || null
      }
    }
  }

  const clearMessages = (sessionId: string) => {
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      session.messages = []
      session.updatedAt = Date.now()
    }
  }

  return {
    // 状态
    sessions,
    currentSessionId,
    isLoading,
    isStreaming,

    // 计算属性
    currentSession,
    currentMessages,

    // 方法
    createSession,
    addMessage,
    deleteSession,
    clearMessages
  }
})

function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}
```

#### 2.4.2 Vue Router路由设计

**路由配置：**
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/chat'
  },
  {
    path: '/chat',
    name: 'Chat',
    component: () => import('@/views/Chat/ChatView.vue'),
    meta: {
      title: '聊天',
      icon: 'chat',
      requiresAuth: false
    }
  },
  {
    path: '/knowledge',
    name: 'Knowledge',
    component: () => import('@/views/Knowledge/KnowledgeView.vue'),
    meta: {
      title: '知识库',
      icon: 'book',
      requiresAuth: false
    },
    children: [
      {
        path: '',
        name: 'KnowledgeList',
        component: () => import('@/views/Knowledge/KnowledgeList.vue')
      },
      {
        path: ':id',
        name: 'KnowledgeDetail',
        component: () => import('@/views/Knowledge/KnowledgeDetail.vue'),
        props: true
      }
    ]
  },
  {
    path: '/models',
    name: 'Models',
    component: () => import('@/views/Models/ModelsView.vue'),
    meta: {
      title: '模型管理',
      icon: 'cpu',
      requiresAuth: false
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings/SettingsView.vue'),
    meta: {
      title: '设置',
      icon: 'settings',
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - AI Studio`
  }

  next()
})

export default router
```

---

## 第三部分：后端架构设计

### 3.1 Rust后端目录结构

#### 3.1.1 项目目录结构

```
src-tauri/
├── Cargo.toml             # Rust项目配置
├── tauri.conf.json        # Tauri配置文件
├── build.rs               # 构建脚本
├── src/                   # 源代码目录
│   ├── main.rs            # 应用入口
│   ├── lib.rs             # 库入口
│   ├── commands/          # Tauri命令
│   │   ├── mod.rs         # 命令模块入口
│   │   ├── chat.rs        # 聊天相关命令
│   │   ├── knowledge.rs   # 知识库相关命令
│   │   ├── models.rs      # 模型管理命令
│   │   ├── multimodal.rs  # 多模态处理命令
│   │   ├── network.rs     # 网络功能命令
│   │   └── system.rs      # 系统管理命令
│   ├── services/          # 业务服务层
│   │   ├── mod.rs         # 服务模块入口
│   │   ├── chat_service.rs      # 聊天服务
│   │   ├── knowledge_service.rs # 知识库服务
│   │   ├── model_service.rs     # 模型服务
│   │   ├── ai_service.rs        # AI推理服务
│   │   └── storage_service.rs   # 存储服务
│   ├── models/            # 数据模型
│   │   ├── mod.rs         # 模型模块入口
│   │   ├── chat.rs        # 聊天数据模型
│   │   ├── knowledge.rs   # 知识库数据模型
│   │   ├── ai_model.rs    # AI模型数据结构
│   │   └── common.rs      # 通用数据结构
│   ├── database/          # 数据库层
│   │   ├── mod.rs         # 数据库模块入口
│   │   ├── sqlite.rs      # SQLite数据库
│   │   ├── chroma.rs      # ChromaDB向量数据库
│   │   └── migrations/    # 数据库迁移
│   ├── ai/                # AI推理引擎
│   │   ├── mod.rs         # AI模块入口
│   │   ├── candle_engine.rs     # Candle推理引擎
│   │   ├── llama_engine.rs      # LLaMA推理引擎
│   │   ├── onnx_engine.rs       # ONNX推理引擎
│   │   └── embedding.rs         # 向量化服务
│   ├── network/           # 网络通信
│   │   ├── mod.rs         # 网络模块入口
│   │   ├── p2p.rs         # P2P通信
│   │   ├── discovery.rs   # 设备发现
│   │   └── transfer.rs    # 文件传输
│   ├── plugins/           # 插件系统
│   │   ├── mod.rs         # 插件模块入口
│   │   ├── manager.rs     # 插件管理器
│   │   ├── runtime.rs     # 插件运行时
│   │   └── api.rs         # 插件API
│   ├── utils/             # 工具函数
│   │   ├── mod.rs         # 工具模块入口
│   │   ├── config.rs      # 配置管理
│   │   ├── logger.rs      # 日志系统
│   │   ├── crypto.rs      # 加密工具
│   │   └── file.rs        # 文件操作
│   └── error.rs           # 错误处理
├── icons/                 # 应用图标
└── resources/             # 资源文件
```

#### 3.1.2 核心模块说明

**main.rs - 应用入口：**
```rust
// src/main.rs
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::Manager;
use ai_studio::{
    commands,
    services::ServiceManager,
    utils::{config::Config, logger::Logger},
};

#[tokio::main]
async fn main() {
    // 初始化日志系统
    Logger::init().expect("Failed to initialize logger");

    // 加载配置
    let config = Config::load().expect("Failed to load config");

    // 初始化服务管理器
    let service_manager = ServiceManager::new(config).await
        .expect("Failed to initialize service manager");

    tauri::Builder::default()
        .manage(service_manager)
        .invoke_handler(tauri::generate_handler![
            commands::chat::send_message,
            commands::chat::get_sessions,
            commands::knowledge::add_document,
            commands::knowledge::search_documents,
            commands::models::list_models,
            commands::models::load_model,
            commands::system::get_system_info,
        ])
        .setup(|app| {
            // 应用初始化逻辑
            let window = app.get_window("main").unwrap();
            window.set_title("AI Studio").unwrap();
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("Error while running tauri application");
}
```

### 3.2 Tauri集成与命令系统

#### 3.2.1 命令系统架构

**Tauri命令架构图：**
```
┌─────────────────────────────────────────────────────────────┐
│                    Tauri命令系统架构                        │
├─────────────────────────────────────────────────────────────┤
│  前端 (Vue3)                                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   invoke    │ │   listen    │ │    emit     │           │
│  │  调用命令    │ │  监听事件    │ │  发送事件    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         ↓                ↓                ↓                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 IPC Bridge                              │ │
│  │              (JSON-RPC 协议)                           │ │
│  └─────────────────────────────────────────────────────────┘ │
│         │                │                │                │
│         ↓                ↓                ↓                │
│  后端 (Rust)                                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Command   │ │   Event     │ │   State     │           │
│  │   处理器     │ │   发射器     │ │   管理器     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

#### 3.2.2 命令实现示例

**聊天命令实现：**
```rust
// src/commands/chat.rs
use tauri::{command, State, Window};
use serde::{Deserialize, Serialize};
use crate::services::ServiceManager;
use crate::models::chat::{ChatMessage, ChatSession};
use crate::error::Result;

#[derive(Debug, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub session_id: String,
    pub content: String,
    pub model: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SendMessageResponse {
    pub message_id: String,
    pub content: String,
    pub tokens: u32,
}

#[command]
pub async fn send_message(
    request: SendMessageRequest,
    service_manager: State<'_, ServiceManager>,
    window: Window,
) -> Result<SendMessageResponse> {
    let chat_service = service_manager.chat_service();

    // 创建用户消息
    let user_message = ChatMessage {
        id: uuid::Uuid::new_v4().to_string(),
        session_id: request.session_id.clone(),
        role: "user".to_string(),
        content: request.content.clone(),
        timestamp: chrono::Utc::now(),
        tokens: None,
    };

    // 保存用户消息
    chat_service.save_message(&user_message).await?;

    // 发送事件通知前端
    window.emit("message_added", &user_message)?;

    // 调用AI服务生成回复
    let ai_service = service_manager.ai_service();
    let response = ai_service.generate_response(
        &request.session_id,
        &request.content,
        &request.model,
    ).await?;

    // 创建AI回复消息
    let ai_message = ChatMessage {
        id: uuid::Uuid::new_v4().to_string(),
        session_id: request.session_id,
        role: "assistant".to_string(),
        content: response.content.clone(),
        timestamp: chrono::Utc::now(),
        tokens: Some(response.tokens),
    };

    // 保存AI消息
    chat_service.save_message(&ai_message).await?;

    // 发送事件通知前端
    window.emit("message_added", &ai_message)?;

    Ok(SendMessageResponse {
        message_id: ai_message.id,
        content: response.content,
        tokens: response.tokens,
    })
}

#[command]
pub async fn get_sessions(
    service_manager: State<'_, ServiceManager>,
) -> Result<Vec<ChatSession>> {
    let chat_service = service_manager.chat_service();
    chat_service.get_all_sessions().await
}

#[command]
pub async fn create_session(
    title: String,
    model: String,
    service_manager: State<'_, ServiceManager>,
) -> Result<ChatSession> {
    let chat_service = service_manager.chat_service();

    let session = ChatSession {
        id: uuid::Uuid::new_v4().to_string(),
        title,
        model,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
        message_count: 0,
    };

    chat_service.create_session(&session).await?;
    Ok(session)
}

#[command]
pub async fn delete_session(
    session_id: String,
    service_manager: State<'_, ServiceManager>,
) -> Result<()> {
    let chat_service = service_manager.chat_service();
    chat_service.delete_session(&session_id).await
}
```

### 3.3 AI推理引擎模块

#### 3.3.1 AI引擎架构设计

**AI推理引擎架构图：**
```
┌─────────────────────────────────────────────────────────────┐
│                    AI推理引擎架构                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Candle    │ │  LLaMA.cpp  │ │ONNX Runtime │           │
│  │   Engine    │ │   Engine    │ │   Engine    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│                          ↓                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              AI Service Manager                         │ │
│  │  • 模型加载管理  • 推理调度  • 资源分配  • 缓存管理     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                          │                                 │
│                          ↓                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  Tokenizer  │ │  Embedding  │ │  Post       │           │
│  │  Manager    │ │  Service    │ │  Processor  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

#### 3.3.2 AI服务实现

**AI服务核心实现：**
```rust
// src/ai/mod.rs
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::error::Result;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationRequest {
    pub prompt: String,
    pub model: String,
    pub max_tokens: u32,
    pub temperature: f32,
    pub top_p: f32,
    pub stop_sequences: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerationResponse {
    pub content: String,
    pub tokens: u32,
    pub finish_reason: String,
    pub model: String,
}

#[async_trait]
pub trait InferenceEngine: Send + Sync {
    async fn load_model(&mut self, model_path: &str) -> Result<()>;
    async fn unload_model(&mut self) -> Result<()>;
    async fn generate(&self, request: GenerationRequest) -> Result<GenerationResponse>;
    async fn generate_stream(&self, request: GenerationRequest) -> Result<tokio::sync::mpsc::Receiver<String>>;
    fn is_loaded(&self) -> bool;
    fn model_info(&self) -> Option<ModelInfo>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelInfo {
    pub name: String,
    pub size: u64,
    pub parameters: u64,
    pub context_length: u32,
    pub architecture: String,
}

pub struct AIServiceManager {
    engines: HashMap<String, Box<dyn InferenceEngine>>,
    current_model: Option<String>,
    config: AIConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIConfig {
    pub default_model: String,
    pub max_concurrent_requests: usize,
    pub cache_size: usize,
    pub gpu_enabled: bool,
    pub gpu_layers: u32,
}

impl AIServiceManager {
    pub fn new(config: AIConfig) -> Self {
        let mut engines: HashMap<String, Box<dyn InferenceEngine>> = HashMap::new();

        // 注册不同的推理引擎
        engines.insert("candle".to_string(), Box::new(CandleEngine::new()));
        engines.insert("llama".to_string(), Box::new(LlamaEngine::new()));
        engines.insert("onnx".to_string(), Box::new(OnnxEngine::new()));

        Self {
            engines,
            current_model: None,
            config,
        }
    }

    pub async fn load_model(&mut self, model_path: &str, engine_type: &str) -> Result<()> {
        if let Some(engine) = self.engines.get_mut(engine_type) {
            engine.load_model(model_path).await?;
            self.current_model = Some(engine_type.to_string());
            Ok(())
        } else {
            Err(crate::error::Error::EngineNotFound(engine_type.to_string()))
        }
    }

    pub async fn generate(&self, request: GenerationRequest) -> Result<GenerationResponse> {
        if let Some(model_name) = &self.current_model {
            if let Some(engine) = self.engines.get(model_name) {
                engine.generate(request).await
            } else {
                Err(crate::error::Error::ModelNotLoaded)
            }
        } else {
            Err(crate::error::Error::NoModelLoaded)
        }
    }

    pub async fn generate_stream(&self, request: GenerationRequest) -> Result<tokio::sync::mpsc::Receiver<String>> {
        if let Some(model_name) = &self.current_model {
            if let Some(engine) = self.engines.get(model_name) {
                engine.generate_stream(request).await
            } else {
                Err(crate::error::Error::ModelNotLoaded)
            }
        } else {
            Err(crate::error::Error::NoModelLoaded)
        }
    }
}
```

### 3.4 后端服务架构设计

#### 3.4.1 服务层架构

**服务层架构图：**
```
┌─────────────────────────────────────────────────────────────┐
│                    服务层架构设计                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Chat Service│ │Knowledge Svc│ │Model Service│           │
│  │             │ │             │ │             │           │
│  │ • 会话管理   │ │ • 文档处理   │ │ • 模型管理   │           │
│  │ • 消息处理   │ │ • 向量化    │ │ • 下载管理   │           │
│  │ • 上下文    │ │ • 搜索引擎   │ │ • 性能监控   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │System Service│ │Storage Svc  │ │Network Svc  │           │
│  │             │ │             │ │             │           │
│  │ • 系统监控   │ │ • 数据存储   │ │ • P2P通信   │           │
│  │ • 配置管理   │ │ • 文件管理   │ │ • 设备发现   │           │
│  │ • 日志管理   │ │ • 缓存管理   │ │ • 文件传输   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

#### 3.4.2 服务管理器实现

**ServiceManager核心实现：**
```rust
// src/services/mod.rs
use std::sync::Arc;
use tokio::sync::RwLock;
use crate::database::{SqliteManager, ChromaManager};
use crate::ai::AIServiceManager;
use crate::utils::config::Config;
use crate::error::Result;

pub mod chat_service;
pub mod knowledge_service;
pub mod model_service;
pub mod system_service;
pub mod storage_service;
pub mod network_service;

use chat_service::ChatService;
use knowledge_service::KnowledgeService;
use model_service::ModelService;
use system_service::SystemService;
use storage_service::StorageService;
use network_service::NetworkService;

pub struct ServiceManager {
    chat_service: Arc<ChatService>,
    knowledge_service: Arc<KnowledgeService>,
    model_service: Arc<ModelService>,
    system_service: Arc<SystemService>,
    storage_service: Arc<StorageService>,
    network_service: Arc<NetworkService>,
    ai_service: Arc<RwLock<AIServiceManager>>,
    sqlite_manager: Arc<SqliteManager>,
    chroma_manager: Arc<RwLock<ChromaManager>>,
}

impl ServiceManager {
    pub async fn new(config: Config) -> Result<Self> {
        // 初始化数据库连接
        let sqlite_manager = Arc::new(SqliteManager::new(&config.database.sqlite_path).await?);
        let chroma_manager = Arc::new(RwLock::new(
            ChromaManager::new(&config.database.chroma_host, config.database.chroma_port).await?
        ));

        // 初始化AI服务
        let ai_service = Arc::new(RwLock::new(AIServiceManager::new(config.ai.clone())));

        // 初始化各个服务
        let storage_service = Arc::new(StorageService::new(config.storage.clone()));
        let chat_service = Arc::new(ChatService::new(
            sqlite_manager.clone(),
            ai_service.clone(),
        ));
        let knowledge_service = Arc::new(KnowledgeService::new(
            sqlite_manager.clone(),
            chroma_manager.clone(),
            storage_service.clone(),
        ));
        let model_service = Arc::new(ModelService::new(
            sqlite_manager.clone(),
            storage_service.clone(),
            ai_service.clone(),
        ));
        let system_service = Arc::new(SystemService::new(config.system.clone()));
        let network_service = Arc::new(NetworkService::new(config.network.clone()));

        Ok(Self {
            chat_service,
            knowledge_service,
            model_service,
            system_service,
            storage_service,
            network_service,
            ai_service,
            sqlite_manager,
            chroma_manager,
        })
    }

    // 服务访问器
    pub fn chat_service(&self) -> Arc<ChatService> {
        self.chat_service.clone()
    }

    pub fn knowledge_service(&self) -> Arc<KnowledgeService> {
        self.knowledge_service.clone()
    }

    pub fn model_service(&self) -> Arc<ModelService> {
        self.model_service.clone()
    }

    pub fn system_service(&self) -> Arc<SystemService> {
        self.system_service.clone()
    }

    pub fn storage_service(&self) -> Arc<StorageService> {
        self.storage_service.clone()
    }

    pub fn network_service(&self) -> Arc<NetworkService> {
        self.network_service.clone()
    }

    pub fn ai_service(&self) -> Arc<RwLock<AIServiceManager>> {
        self.ai_service.clone()
    }
}
```

---

## 第四部分：核心功能模块

### 4.1 聊天功能模块

#### 4.1.1 聊天模块架构

**聊天功能架构图：**
```
┌─────────────────────────────────────────────────────────────┐
│                    聊天功能模块架构                          │
├─────────────────────────────────────────────────────────────┤
│  前端组件层                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ ChatView    │ │MessageList  │ │ ChatInput   │           │
│  │ 聊天视图     │ │ 消息列表     │ │ 输入组件     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│                          ↓                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 Chat Store                              │ │
│  │  • 会话管理  • 消息状态  • 流式响应  • 历史记录         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                          │                                 │
│                          ↓                                 │
│  后端服务层                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Chat Service │ │ AI Service  │ │Context Mgr  │           │
│  │ 聊天服务     │ │ AI推理服务  │ │ 上下文管理   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│                          ↓                                 │
│  数据存储层                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   SQLite    │ │   Cache     │ │   Files     │           │
│  │  会话数据    │ │  临时缓存    │ │  附件文件    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

#### 4.1.2 聊天服务实现

**ChatService核心实现：**
```rust
// src/services/chat_service.rs
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

use crate::database::SqliteManager;
use crate::ai::AIServiceManager;
use crate::models::chat::{ChatSession, ChatMessage};
use crate::error::Result;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatContext {
    pub session_id: String,
    pub messages: Vec<ChatMessage>,
    pub max_context_length: usize,
    pub system_prompt: Option<String>,
}

pub struct ChatService {
    db: Arc<SqliteManager>,
    ai_service: Arc<RwLock<AIServiceManager>>,
    contexts: Arc<RwLock<std::collections::HashMap<String, ChatContext>>>,
}

impl ChatService {
    pub fn new(
        db: Arc<SqliteManager>,
        ai_service: Arc<RwLock<AIServiceManager>>,
    ) -> Self {
        Self {
            db,
            ai_service,
            contexts: Arc::new(RwLock::new(std::collections::HashMap::new())),
        }
    }

    pub async fn create_session(&self, title: &str, model: &str) -> Result<ChatSession> {
        let session = ChatSession {
            id: Uuid::new_v4().to_string(),
            title: title.to_string(),
            model: model.to_string(),
            system_prompt: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
            message_count: 0,
            total_tokens: 0,
            is_archived: false,
        };

        // 保存到数据库
        self.db.create_chat_session(&session).await?;

        // 初始化上下文
        let context = ChatContext {
            session_id: session.id.clone(),
            messages: Vec::new(),
            max_context_length: 4000,
            system_prompt: None,
        };

        self.contexts.write().await.insert(session.id.clone(), context);

        Ok(session)
    }

    pub async fn send_message(
        &self,
        session_id: &str,
        content: &str,
        role: &str,
    ) -> Result<ChatMessage> {
        let message = ChatMessage {
            id: Uuid::new_v4().to_string(),
            session_id: session_id.to_string(),
            role: role.to_string(),
            content: content.to_string(),
            timestamp: Utc::now(),
            tokens: None,
            parent_id: None,
            metadata: None,
        };

        // 保存消息到数据库
        self.db.save_chat_message(&message).await?;

        // 更新上下文
        self.update_context(session_id, &message).await?;

        Ok(message)
    }

    pub async fn generate_response(
        &self,
        session_id: &str,
        user_message: &str,
    ) -> Result<ChatMessage> {
        // 获取上下文
        let context = self.get_context(session_id).await?;

        // 构建提示词
        let prompt = self.build_prompt(&context, user_message).await?;

        // 调用AI服务生成回复
        let ai_service = self.ai_service.read().await;
        let request = crate::ai::GenerationRequest {
            prompt,
            model: context.session_id.clone(), // 这里应该是模型名称
            max_tokens: 2048,
            temperature: 0.7,
            top_p: 0.9,
            stop_sequences: vec!["Human:".to_string(), "Assistant:".to_string()],
        };

        let response = ai_service.generate(request).await?;

        // 创建AI回复消息
        let ai_message = ChatMessage {
            id: Uuid::new_v4().to_string(),
            session_id: session_id.to_string(),
            role: "assistant".to_string(),
            content: response.content,
            timestamp: Utc::now(),
            tokens: Some(response.tokens),
            parent_id: None,
            metadata: None,
        };

        // 保存AI消息
        self.db.save_chat_message(&ai_message).await?;

        // 更新上下文
        self.update_context(session_id, &ai_message).await?;

        Ok(ai_message)
    }

    async fn update_context(&self, session_id: &str, message: &ChatMessage) -> Result<()> {
        let mut contexts = self.contexts.write().await;
        if let Some(context) = contexts.get_mut(session_id) {
            context.messages.push(message.clone());

            // 如果上下文过长，移除最早的消息
            while context.messages.len() > context.max_context_length {
                context.messages.remove(0);
            }
        }
        Ok(())
    }

    async fn get_context(&self, session_id: &str) -> Result<ChatContext> {
        let contexts = self.contexts.read().await;
        contexts.get(session_id)
            .cloned()
            .ok_or_else(|| crate::error::Error::SessionNotFound(session_id.to_string()))
    }

    async fn build_prompt(&self, context: &ChatContext, user_message: &str) -> Result<String> {
        let mut prompt = String::new();

        // 添加系统提示词
        if let Some(system_prompt) = &context.system_prompt {
            prompt.push_str(&format!("System: {}\n\n", system_prompt));
        }

        // 添加历史消息
        for message in &context.messages {
            prompt.push_str(&format!("{}: {}\n",
                if message.role == "user" { "Human" } else { "Assistant" },
                message.content
            ));
        }

        // 添加当前用户消息
        prompt.push_str(&format!("Human: {}\nAssistant: ", user_message));

        Ok(prompt)
    }

    pub async fn get_session_messages(&self, session_id: &str) -> Result<Vec<ChatMessage>> {
        self.db.get_chat_messages(session_id).await
    }

    pub async fn delete_session(&self, session_id: &str) -> Result<()> {
        // 从数据库删除
        self.db.delete_chat_session(session_id).await?;

        // 从内存中移除上下文
        self.contexts.write().await.remove(session_id);

        Ok(())
    }
}
```

### 4.2 知识库模块

#### 4.2.1 知识库架构设计

**知识库模块架构图：**
```
┌─────────────────────────────────────────────────────────────┐
│                    知识库模块架构                            │
├─────────────────────────────────────────────────────────────┤
│  文档处理层                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ File Parser │ │Text Splitter│ │ Metadata    │           │
│  │ 文件解析器   │ │ 文本分块器   │ │ 元数据提取   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│                          ↓                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              向量化处理层                               │ │
│  │  • 文本向量化  • 批量处理  • 质量检查  • 索引构建       │ │
│  └─────────────────────────────────────────────────────────┘ │
│                          │                                 │
│                          ↓                                 │
│  存储层                                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   SQLite    │ │  ChromaDB   │ │ File System │           │
│  │  元数据存储  │ │  向量存储    │ │  原文件存储  │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│                          ↓                                 │
│  检索层                                                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Vector Search│ │Hybrid Search│ │ Reranking   │           │
│  │ 向量搜索     │ │ 混合搜索     │ │ 重排序      │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

#### 4.2.2 知识库服务实现

**KnowledgeService核心实现：**
```rust
// src/services/knowledge_service.rs
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use serde::{Deserialize, Serialize};

use crate::database::{SqliteManager, ChromaManager};
use crate::services::StorageService;
use crate::models::knowledge::{KnowledgeBase, Document, DocumentChunk};
use crate::error::Result;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchRequest {
    pub query: String,
    pub knowledge_base_id: String,
    pub limit: usize,
    pub threshold: f32,
    pub search_type: SearchType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SearchType {
    Vector,
    Keyword,
    Hybrid,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub chunk_id: String,
    pub content: String,
    pub score: f32,
    pub document_id: String,
    pub document_title: String,
    pub metadata: serde_json::Value,
}

pub struct KnowledgeService {
    db: Arc<SqliteManager>,
    vector_db: Arc<RwLock<ChromaManager>>,
    storage: Arc<StorageService>,
}

impl KnowledgeService {
    pub fn new(
        db: Arc<SqliteManager>,
        vector_db: Arc<RwLock<ChromaManager>>,
        storage: Arc<StorageService>,
    ) -> Self {
        Self {
            db,
            vector_db,
            storage,
        }
    }

    pub async fn create_knowledge_base(
        &self,
        name: &str,
        description: &str,
        embedding_model: &str,
    ) -> Result<KnowledgeBase> {
        let kb = KnowledgeBase {
            id: Uuid::new_v4().to_string(),
            name: name.to_string(),
            description: description.to_string(),
            embedding_model: embedding_model.to_string(),
            chunk_size: 1000,
            chunk_overlap: 200,
            vector_dimension: 768,
            document_count: 0,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        // 保存到数据库
        self.db.create_knowledge_base(&kb).await?;

        // 在向量数据库中创建集合
        let mut vector_db = self.vector_db.write().await;
        vector_db.create_collection(&kb.id, kb.vector_dimension, "cosine").await?;

        Ok(kb)
    }

    pub async fn add_document(
        &self,
        knowledge_base_id: &str,
        file_path: &str,
        title: Option<String>,
    ) -> Result<Document> {
        // 解析文件
        let content = self.parse_file(file_path).await?;

        // 创建文档记录
        let document = Document {
            id: Uuid::new_v4().to_string(),
            knowledge_base_id: knowledge_base_id.to_string(),
            title: title.unwrap_or_else(|| {
                std::path::Path::new(file_path)
                    .file_stem()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string()
            }),
            file_path: file_path.to_string(),
            file_type: self.detect_file_type(file_path),
            file_size: self.get_file_size(file_path).await?,
            content_hash: self.calculate_hash(&content),
            processing_status: "processing".to_string(),
            chunk_count: 0,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        // 保存文档记录
        self.db.create_document(&document).await?;

        // 异步处理文档分块和向量化
        let service = self.clone();
        let doc_id = document.id.clone();
        let kb_id = knowledge_base_id.to_string();

        tokio::spawn(async move {
            if let Err(e) = service.process_document(&doc_id, &kb_id, &content).await {
                log::error!("Failed to process document {}: {}", doc_id, e);
                // 更新处理状态为失败
                let _ = service.db.update_document_status(&doc_id, "failed").await;
            }
        });

        Ok(document)
    }

    async fn process_document(
        &self,
        document_id: &str,
        knowledge_base_id: &str,
        content: &str,
    ) -> Result<()> {
        // 获取知识库配置
        let kb = self.db.get_knowledge_base(knowledge_base_id).await?;

        // 文本分块
        let chunks = self.split_text(content, kb.chunk_size, kb.chunk_overlap);

        // 向量化处理
        let mut vector_documents = Vec::new();

        for (index, chunk) in chunks.iter().enumerate() {
            let chunk_id = format!("{}_{}", document_id, index);

            // 生成向量
            let embedding = self.generate_embedding(chunk, &kb.embedding_model).await?;

            // 创建文档块记录
            let doc_chunk = DocumentChunk {
                id: chunk_id.clone(),
                document_id: document_id.to_string(),
                knowledge_base_id: knowledge_base_id.to_string(),
                chunk_index: index as i32,
                content: chunk.clone(),
                content_hash: self.calculate_hash(chunk),
                token_count: self.count_tokens(chunk),
                embedding_status: "completed".to_string(),
                vector_id: Some(chunk_id.clone()),
                created_at: chrono::Utc::now(),
            };

            // 保存到数据库
            self.db.create_document_chunk(&doc_chunk).await?;

            // 准备向量文档
            let vector_doc = crate::database::chroma::VectorDocument {
                id: chunk_id,
                content: chunk.clone(),
                embedding,
                metadata: serde_json::json!({
                    "document_id": document_id,
                    "knowledge_base_id": knowledge_base_id,
                    "chunk_index": index
                }).as_object().unwrap().clone(),
            };

            vector_documents.push(vector_doc);
        }

        // 批量添加到向量数据库
        let mut vector_db = self.vector_db.write().await;
        vector_db.add_documents_batch(knowledge_base_id, vector_documents, 100).await?;

        // 更新文档状态
        self.db.update_document_status(document_id, "completed").await?;
        self.db.update_document_chunk_count(document_id, chunks.len() as i32).await?;

        Ok(())
    }

    pub async fn search(
        &self,
        request: SearchRequest,
    ) -> Result<Vec<SearchResult>> {
        match request.search_type {
            SearchType::Vector => self.vector_search(&request).await,
            SearchType::Keyword => self.keyword_search(&request).await,
            SearchType::Hybrid => self.hybrid_search(&request).await,
        }
    }

    async fn vector_search(&self, request: &SearchRequest) -> Result<Vec<SearchResult>> {
        let vector_db = self.vector_db.read().await;

        let search_options = crate::database::chroma::SearchOptions {
            limit: request.limit,
            threshold: request.threshold,
            include_metadata: true,
            include_content: true,
            filters: None,
        };

        let results = vector_db.search(
            &request.knowledge_base_id,
            &request.query,
            search_options,
        ).await?;

        let mut search_results = Vec::new();

        for result in results {
            if let Some(document_id) = result.metadata.get("document_id") {
                if let Some(document) = self.db.get_document(
                    document_id.as_str().unwrap()
                ).await.ok() {
                    search_results.push(SearchResult {
                        chunk_id: result.id,
                        content: result.content,
                        score: result.score,
                        document_id: document.id,
                        document_title: document.title,
                        metadata: serde_json::to_value(&result.metadata)?,
                    });
                }
            }
        }

        Ok(search_results)
    }

    async fn keyword_search(&self, request: &SearchRequest) -> Result<Vec<SearchResult>> {
        // 实现关键词搜索逻辑
        let chunks = self.db.search_document_chunks_by_keyword(
            &request.knowledge_base_id,
            &request.query,
            request.limit,
        ).await?;

        let mut results = Vec::new();

        for chunk in chunks {
            if let Some(document) = self.db.get_document(&chunk.document_id).await.ok() {
                results.push(SearchResult {
                    chunk_id: chunk.id,
                    content: chunk.content,
                    score: 1.0, // 关键词搜索使用固定分数
                    document_id: document.id,
                    document_title: document.title,
                    metadata: serde_json::json!({}),
                });
            }
        }

        Ok(results)
    }

    async fn hybrid_search(&self, request: &SearchRequest) -> Result<Vec<SearchResult>> {
        // 组合向量搜索和关键词搜索结果
        let vector_results = self.vector_search(request).await?;
        let keyword_results = self.keyword_search(request).await?;

        // 简单的结果合并和重排序逻辑
        let mut combined_results = vector_results;
        combined_results.extend(keyword_results);

        // 去重和重排序
        combined_results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());
        combined_results.truncate(request.limit);

        Ok(combined_results)
    }

    // 辅助方法
    async fn parse_file(&self, file_path: &str) -> Result<String> {
        // 根据文件类型解析内容
        match self.detect_file_type(file_path).as_str() {
            "txt" => self.storage.read_text_file(file_path).await,
            "pdf" => self.parse_pdf(file_path).await,
            "docx" => self.parse_docx(file_path).await,
            "md" => self.storage.read_text_file(file_path).await,
            _ => Err(crate::error::Error::UnsupportedFileType(file_path.to_string())),
        }
    }

    fn detect_file_type(&self, file_path: &str) -> String {
        std::path::Path::new(file_path)
            .extension()
            .unwrap_or_default()
            .to_string_lossy()
            .to_lowercase()
    }

    async fn get_file_size(&self, file_path: &str) -> Result<u64> {
        let metadata = tokio::fs::metadata(file_path).await?;
        Ok(metadata.len())
    }

    fn calculate_hash(&self, content: &str) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(content.as_bytes());
        format!("{:x}", hasher.finalize())
    }

    fn split_text(&self, text: &str, chunk_size: usize, overlap: usize) -> Vec<String> {
        let mut chunks = Vec::new();
        let chars: Vec<char> = text.chars().collect();

        let mut start = 0;
        while start < chars.len() {
            let end = std::cmp::min(start + chunk_size, chars.len());
            let chunk: String = chars[start..end].iter().collect();
            chunks.push(chunk);

            if end >= chars.len() {
                break;
            }

            start = end - overlap;
        }

        chunks
    }

    async fn generate_embedding(&self, text: &str, model: &str) -> Result<Vec<f32>> {
        // 这里应该调用实际的向量化服务
        // 暂时返回随机向量作为示例
        Ok((0..768).map(|_| rand::random::<f32>()).collect())
    }

    fn count_tokens(&self, text: &str) -> i32 {
        // 简单的token计数，实际应该使用tokenizer
        text.split_whitespace().count() as i32
    }
}
```

### 4.3 模型管理模块

#### 4.3.1 模型管理架构

**模型管理模块架构图：**
```
┌─────────────────────────────────────────────────────────────┐
│                    模型管理模块架构                          │
├─────────────────────────────────────────────────────────────┤
│  模型发现层                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Model Registry│ │ HuggingFace │ │Local Scanner│           │
│  │ 模型注册表   │ │   Hub       │ │ 本地扫描器   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│                          ↓                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              模型管理服务                               │ │
│  │  • 下载管理  • 版本控制  • 存储管理  • 性能监控         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                          │                                 │
│                          ↓                                 │
│  运行时管理层                                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Model Loader │ │Memory Mgr   │ │Performance  │           │
│  │ 模型加载器   │ │ 内存管理器   │ │ 性能监控器   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

#### 4.3.2 模型管理服务实现

**ModelService核心实现：**
```rust
// src/services/model_service.rs
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

use crate::database::SqliteManager;
use crate::services::StorageService;
use crate::ai::AIServiceManager;
use crate::models::ai_model::{AIModel, ModelInfo, DownloadProgress};
use crate::error::Result;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelDownloadRequest {
    pub model_id: String,
    pub source: ModelSource,
    pub destination: PathBuf,
    pub quantization: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ModelSource {
    HuggingFace { repo_id: String, revision: Option<String> },
    Local { path: PathBuf },
    Url { url: String },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelPerformanceMetrics {
    pub model_id: String,
    pub tokens_per_second: f32,
    pub memory_usage: u64,
    pub gpu_utilization: f32,
    pub latency_ms: f32,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

pub struct ModelService {
    db: Arc<SqliteManager>,
    storage: Arc<StorageService>,
    ai_service: Arc<RwLock<AIServiceManager>>,
    download_manager: Arc<RwLock<DownloadManager>>,
    performance_monitor: Arc<RwLock<PerformanceMonitor>>,
}

impl ModelService {
    pub fn new(
        db: Arc<SqliteManager>,
        storage: Arc<StorageService>,
        ai_service: Arc<RwLock<AIServiceManager>>,
    ) -> Self {
        Self {
            db,
            storage,
            ai_service,
            download_manager: Arc::new(RwLock::new(DownloadManager::new())),
            performance_monitor: Arc::new(RwLock::new(PerformanceMonitor::new())),
        }
    }

    pub async fn list_available_models(&self) -> Result<Vec<ModelInfo>> {
        // 从数据库获取已安装的模型
        let installed_models = self.db.get_all_models().await?;

        // 扫描本地模型目录
        let local_models = self.scan_local_models().await?;

        // 合并结果
        let mut all_models = installed_models;
        for local_model in local_models {
            if !all_models.iter().any(|m| m.id == local_model.id) {
                all_models.push(local_model);
            }
        }

        Ok(all_models)
    }

    pub async fn download_model(&self, request: ModelDownloadRequest) -> Result<String> {
        let download_id = uuid::Uuid::new_v4().to_string();

        // 创建模型记录
        let model = AIModel {
            id: request.model_id.clone(),
            name: request.model_id.clone(),
            description: None,
            model_type: "llm".to_string(),
            architecture: "transformer".to_string(),
            parameters: 0,
            context_length: 4096,
            file_path: request.destination.to_string_lossy().to_string(),
            file_size: 0,
            quantization: request.quantization.clone(),
            status: "downloading".to_string(),
            download_progress: 0.0,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        // 保存到数据库
        self.db.create_model(&model).await?;

        // 启动下载任务
        let download_manager = self.download_manager.clone();
        let db = self.db.clone();
        let model_id = request.model_id.clone();

        tokio::spawn(async move {
            let mut dm = download_manager.write().await;
            if let Err(e) = dm.start_download(request, &db).await {
                log::error!("Failed to download model {}: {}", model_id, e);
                let _ = db.update_model_status(&model_id, "failed").await;
            }
        });

        Ok(download_id)
    }

    pub async fn load_model(&self, model_id: &str) -> Result<()> {
        // 获取模型信息
        let model = self.db.get_model(model_id).await?;

        // 检查模型文件是否存在
        if !std::path::Path::new(&model.file_path).exists() {
            return Err(crate::error::Error::ModelFileNotFound(model.file_path));
        }

        // 加载模型到AI服务
        let mut ai_service = self.ai_service.write().await;
        ai_service.load_model(&model.file_path, "candle").await?;

        // 更新模型状态
        self.db.update_model_status(model_id, "loaded").await?;

        // 开始性能监控
        let performance_monitor = self.performance_monitor.clone();
        let model_id_clone = model_id.to_string();

        tokio::spawn(async move {
            let mut pm = performance_monitor.write().await;
            pm.start_monitoring(&model_id_clone).await;
        });

        Ok(())
    }

    pub async fn unload_model(&self, model_id: &str) -> Result<()> {
        // 从AI服务卸载模型
        let mut ai_service = self.ai_service.write().await;
        ai_service.unload_model().await?;

        // 更新模型状态
        self.db.update_model_status(model_id, "unloaded").await?;

        // 停止性能监控
        let mut performance_monitor = self.performance_monitor.write().await;
        performance_monitor.stop_monitoring(model_id).await;

        Ok(())
    }

    pub async fn delete_model(&self, model_id: &str) -> Result<()> {
        // 获取模型信息
        let model = self.db.get_model(model_id).await?;

        // 如果模型已加载，先卸载
        if model.status == "loaded" {
            self.unload_model(model_id).await?;
        }

        // 删除模型文件
        if std::path::Path::new(&model.file_path).exists() {
            tokio::fs::remove_file(&model.file_path).await?;
        }

        // 从数据库删除记录
        self.db.delete_model(model_id).await?;

        Ok(())
    }

    pub async fn get_model_performance(&self, model_id: &str) -> Result<Vec<ModelPerformanceMetrics>> {
        self.db.get_model_performance_metrics(model_id).await
    }

    async fn scan_local_models(&self) -> Result<Vec<ModelInfo>> {
        let models_dir = self.storage.get_models_directory();
        let mut models = Vec::new();

        if !models_dir.exists() {
            return Ok(models);
        }

        let mut entries = tokio::fs::read_dir(&models_dir).await?;

        while let Some(entry) = entries.next_entry().await? {
            if entry.file_type().await?.is_dir() {
                if let Some(model_info) = self.parse_model_directory(&entry.path()).await? {
                    models.push(model_info);
                }
            }
        }

        Ok(models)
    }

    async fn parse_model_directory(&self, path: &std::path::Path) -> Result<Option<ModelInfo>> {
        // 查找模型配置文件
        let config_path = path.join("config.json");
        if !config_path.exists() {
            return Ok(None);
        }

        // 解析配置文件
        let config_content = tokio::fs::read_to_string(&config_path).await?;
        let config: serde_json::Value = serde_json::from_str(&config_content)?;

        // 提取模型信息
        let model_info = ModelInfo {
            id: path.file_name()
                .unwrap_or_default()
                .to_string_lossy()
                .to_string(),
            name: config.get("model_name")
                .and_then(|v| v.as_str())
                .unwrap_or("Unknown")
                .to_string(),
            description: config.get("description")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            model_type: config.get("model_type")
                .and_then(|v| v.as_str())
                .unwrap_or("llm")
                .to_string(),
            architecture: config.get("architecture")
                .and_then(|v| v.as_str())
                .unwrap_or("transformer")
                .to_string(),
            parameters: config.get("num_parameters")
                .and_then(|v| v.as_u64())
                .unwrap_or(0),
            context_length: config.get("max_position_embeddings")
                .and_then(|v| v.as_u64())
                .unwrap_or(4096) as u32,
            file_size: self.calculate_directory_size(path).await?,
            quantization: config.get("quantization")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            status: "available".to_string(),
            created_at: chrono::Utc::now(),
        };

        Ok(Some(model_info))
    }

    async fn calculate_directory_size(&self, path: &std::path::Path) -> Result<u64> {
        let mut total_size = 0u64;
        let mut entries = tokio::fs::read_dir(path).await?;

        while let Some(entry) = entries.next_entry().await? {
            let metadata = entry.metadata().await?;
            if metadata.is_file() {
                total_size += metadata.len();
            } else if metadata.is_dir() {
                total_size += self.calculate_directory_size(&entry.path()).await?;
            }
        }

        Ok(total_size)
    }
}

// 下载管理器
struct DownloadManager {
    active_downloads: std::collections::HashMap<String, DownloadTask>,
}

impl DownloadManager {
    fn new() -> Self {
        Self {
            active_downloads: std::collections::HashMap::new(),
        }
    }

    async fn start_download(
        &mut self,
        request: ModelDownloadRequest,
        db: &SqliteManager,
    ) -> Result<()> {
        match request.source {
            ModelSource::HuggingFace { repo_id, revision } => {
                self.download_from_huggingface(&request.model_id, &repo_id, revision, &request.destination, db).await
            }
            ModelSource::Url { url } => {
                self.download_from_url(&request.model_id, &url, &request.destination, db).await
            }
            ModelSource::Local { path } => {
                self.copy_local_model(&request.model_id, &path, &request.destination, db).await
            }
        }
    }

    async fn download_from_huggingface(
        &mut self,
        model_id: &str,
        repo_id: &str,
        revision: Option<String>,
        destination: &std::path::Path,
        db: &SqliteManager,
    ) -> Result<()> {
        // 实现从HuggingFace Hub下载模型的逻辑
        // 这里是简化版本，实际需要使用HuggingFace API

        let client = reqwest::Client::new();
        let base_url = format!("https://huggingface.co/{}/resolve/main", repo_id);

        // 获取模型文件列表
        let files = vec!["config.json", "pytorch_model.bin", "tokenizer.json"];

        for file in files {
            let url = format!("{}/{}", base_url, file);
            let file_path = destination.join(file);

            // 创建目录
            if let Some(parent) = file_path.parent() {
                tokio::fs::create_dir_all(parent).await?;
            }

            // 下载文件
            let response = client.get(&url).send().await?;
            let total_size = response.content_length().unwrap_or(0);

            let mut file = tokio::fs::File::create(&file_path).await?;
            let mut downloaded = 0u64;
            let mut stream = response.bytes_stream();

            use futures_util::StreamExt;
            while let Some(chunk) = stream.next().await {
                let chunk = chunk?;
                tokio::io::AsyncWriteExt::write_all(&mut file, &chunk).await?;
                downloaded += chunk.len() as u64;

                // 更新下载进度
                let progress = if total_size > 0 {
                    (downloaded as f32 / total_size as f32) * 100.0
                } else {
                    0.0
                };

                let _ = db.update_model_download_progress(model_id, progress).await;
            }
        }

        // 更新模型状态
        db.update_model_status(model_id, "downloaded").await?;

        Ok(())
    }

    async fn download_from_url(
        &mut self,
        model_id: &str,
        url: &str,
        destination: &std::path::Path,
        db: &SqliteManager,
    ) -> Result<()> {
        // 实现从URL下载模型的逻辑
        let client = reqwest::Client::new();
        let response = client.get(url).send().await?;
        let total_size = response.content_length().unwrap_or(0);

        let mut file = tokio::fs::File::create(destination).await?;
        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();

        use futures_util::StreamExt;
        while let Some(chunk) = stream.next().await {
            let chunk = chunk?;
            tokio::io::AsyncWriteExt::write_all(&mut file, &chunk).await?;
            downloaded += chunk.len() as u64;

            let progress = if total_size > 0 {
                (downloaded as f32 / total_size as f32) * 100.0
            } else {
                0.0
            };

            let _ = db.update_model_download_progress(model_id, progress).await;
        }

        db.update_model_status(model_id, "downloaded").await?;
        Ok(())
    }

    async fn copy_local_model(
        &mut self,
        model_id: &str,
        source: &std::path::Path,
        destination: &std::path::Path,
        db: &SqliteManager,
    ) -> Result<()> {
        // 复制本地模型文件
        if source.is_file() {
            tokio::fs::copy(source, destination).await?;
        } else if source.is_dir() {
            self.copy_directory(source, destination).await?;
        }

        db.update_model_status(model_id, "downloaded").await?;
        Ok(())
    }

    async fn copy_directory(
        &self,
        source: &std::path::Path,
        destination: &std::path::Path,
    ) -> Result<()> {
        tokio::fs::create_dir_all(destination).await?;

        let mut entries = tokio::fs::read_dir(source).await?;
        while let Some(entry) = entries.next_entry().await? {
            let source_path = entry.path();
            let dest_path = destination.join(entry.file_name());

            if source_path.is_file() {
                tokio::fs::copy(&source_path, &dest_path).await?;
            } else if source_path.is_dir() {
                self.copy_directory(&source_path, &dest_path).await?;
            }
        }

        Ok(())
    }
}

// 性能监控器
struct PerformanceMonitor {
    monitoring_tasks: std::collections::HashMap<String, tokio::task::JoinHandle<()>>,
}

impl PerformanceMonitor {
    fn new() -> Self {
        Self {
            monitoring_tasks: std::collections::HashMap::new(),
        }
    }

    async fn start_monitoring(&mut self, model_id: &str) {
        let model_id_clone = model_id.to_string();

        let task = tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(5));

            loop {
                interval.tick().await;

                // 收集性能指标
                let metrics = ModelPerformanceMetrics {
                    model_id: model_id_clone.clone(),
                    tokens_per_second: Self::measure_tokens_per_second().await,
                    memory_usage: Self::measure_memory_usage().await,
                    gpu_utilization: Self::measure_gpu_utilization().await,
                    latency_ms: Self::measure_latency().await,
                    timestamp: chrono::Utc::now(),
                };

                // 这里应该将指标保存到数据库或发送到监控系统
                log::debug!("Performance metrics for {}: {:?}", model_id_clone, metrics);
            }
        });

        self.monitoring_tasks.insert(model_id.to_string(), task);
    }

    async fn stop_monitoring(&mut self, model_id: &str) {
        if let Some(task) = self.monitoring_tasks.remove(model_id) {
            task.abort();
        }
    }

    async fn measure_tokens_per_second() -> f32 {
        // 实现token/秒测量逻辑
        100.0 // 示例值
    }

    async fn measure_memory_usage() -> u64 {
        // 实现内存使用量测量逻辑
        1024 * 1024 * 1024 // 示例值：1GB
    }

    async fn measure_gpu_utilization() -> f32 {
        // 实现GPU利用率测量逻辑
        75.0 // 示例值：75%
    }

    async fn measure_latency() -> f32 {
        // 实现延迟测量逻辑
        50.0 // 示例值：50ms
    }
}

struct DownloadTask {
    id: String,
    model_id: String,
    progress: f32,
    status: String,
}
```

### 4.4 多模态交互模块

#### 4.4.1 多模态架构设计

**多模态交互模块架构图：**
```
┌─────────────────────────────────────────────────────────────┐
│                    多模态交互模块架构                        │
├─────────────────────────────────────────────────────────────┤
│  输入处理层                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Image Processor│ │Audio Processor│ │Video Processor│       │
│  │ 图像处理器   │ │ 音频处理器   │ │ 视频处理器   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │                │                │                │
│         └────────────────┼────────────────┘                │
│                          │                                 │
│                          ↓                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              AI推理引擎层                               │ │
│  │  • OCR识别  • 语音转文本  • 图像理解  • 视频分析       │ │
│  └─────────────────────────────────────────────────────────┘ │
│                          │                                 │
│                          ↓                                 │
│  输出生成层                                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │Text Output  │ │Speech Synth │ │Image Gen    │           │
│  │ 文本输出     │ │ 语音合成     │ │ 图像生成     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```